#!/bin/bash
# Copyright 2021 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================

# Update all yarn.lock file in this tfjs-models repository.

update() {
  rm yarn.lock && yarn
  cd ".."
}

for dir in */; do
    cd "$dir"
    echo `pwd`
    update

    cd "$dir""demo"
    if [ $? -eq 0 ]; then
      echo `pwd`
      update
      cd ".."
    fi
done

