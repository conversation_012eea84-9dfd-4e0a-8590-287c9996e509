/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import {MediaPipeSelfieSegmentationMediaPipeModelConfig, MediaPipeSelfieSegmentationMediaPipeSegmentationConfig} from './types';

export const DEFAULT_MEDIAPIPE_SELFIE_SEGMENTATION_MODEL_CONFIG:
    MediaPipeSelfieSegmentationMediaPipeModelConfig = {
      runtime: 'mediapipe',
      modelType: 'general'
    };

export const DEFAULT_MEDIAPIPE_SELFIE_SEGMENTATION_SEGMENTATION_CONFIG:
    MediaPipeSelfieSegmentationMediaPipeSegmentationConfig = {
      flipHorizontal: false,
    };
