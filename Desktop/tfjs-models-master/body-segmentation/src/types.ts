/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import {PixelInput} from './shared/calculators/interfaces/common_interfaces';

export enum SupportedModels {
  BodyPix = 'BodyPix',
  MediaPipeSelfieSegmentation = 'MediaPipeSelfieSegmentation',
}

/**
 * Common config to create the body segmenter.
 */
export interface ModelConfig {}

/**
 * Common config for the `segmentPeople` method.
 *
 * `flipHorizontal`: Optional. Default to false. In some cases, the image is
 * mirrored, e.g. video stream from camera, flipHorizontal will flip the
 * keypoints horizontally.
 */
export interface SegmentationConfig {
  flipHorizontal?: boolean;
}

/**
 * Allowed input format for the `segmentPeople` method.
 */
export type BodySegmenterInput = PixelInput;
