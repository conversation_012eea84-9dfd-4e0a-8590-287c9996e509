<!DOCTYPE html>
<html>

<head>
  <title>BodyPix - With a Webcam Demo</title>
  <style>
    body {
      margin: 0;
      width: 100%;
    }

    .footer {
      position: fixed;
      left: 0;
      bottom: 0;
      width: 100%;
      color: black;
    }

    .footer-text {
      max-width: 600px;
      text-align: center;
      margin: auto;
    }

    .footer-text,
    #colors {
      font-family: Arial, Helvetica, sans-serif;
    }

    #colors {
      margin: 0;
      padding: 0;
      list-style: none;
    }

    #colors li {
      margin: 0;
    }

    #colors .color {
      width: 50px;
      height: 1em;
    }

    #colors li div {
      display: inline-block;
    }

    .footer-menu {
      display: none;
    }

    /*
     *  The following loading spinner CSS is from SpinKit project
     *  https://github.com/tobiasahlin/SpinKit
     */
    .sk-spinner-pulse {
      width: 20px;
      height: 20px;
      margin: auto 10px;
      float: left;
      background-color: #333;
      border-radius: 100%;
      -webkit-animation: sk-pulseScaleOut 1s infinite ease-in-out;
      animation: sk-pulseScaleOut 1s infinite ease-in-out;
    }

    @-webkit-keyframes sk-pulseScaleOut {
      0% {
        -webkit-transform: scale(0);
        transform: scale(0);
      }

      100% {
        -webkit-transform: scale(1.0);
        transform: scale(1.0);
        opacity: 0;
      }
    }

    @keyframes sk-pulseScaleOut {
      0% {
        -webkit-transform: scale(0);
        transform: scale(0);
      }

      100% {
        -webkit-transform: scale(1.0);
        transform: scale(1.0);
        opacity: 0;
      }
    }

    .spinner-text {
      float: left;
    }

    /* mobile */
    @media only screen and (max-width: 850px) {
      .footer {
        height: 46px;
      }

      .footer-text {
        display: none;
      }

      #main {
        width: 100%;
        height: 100%;
        overflow: hidden;
      }
    }
  </style>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta charset="UTF-8">
</head>

<body>
  <div id="stats"></div>
  <div id="info" style='display:none'>
  </div>
  <div id="loading" style='display:flex'>
    <div class="spinner-text">
      Loading BodyPix model...
    </div>
    <div class="sk-spinner sk-spinner-pulse"></div>
  </div>
  <div id='main' style='display:none'>
    <video id="video" playsinline style="display: none;"></video>
    <canvas id="output">
  </div>
  <ul id="colors" style="display: none"></ul>
  <div class="footer">
    <div class="footer-text">
      <p>
        The BodyPix model can estimate which pixels in an image are part of a person, and which pixels
        are part of each of 24 body parts. It works for <strong>multiple people</strong> in an input image or video.
        <br>
        <br> The <strong>internal resolution</strong>, <strong>output stride</strong> and <strong>model</strong> have
        the
        largest effects on accuracy/speed. A <i>higher</i> internal resolution results in higher accuracy but lower the
        speed.
        A <i>higher</i> output stride results in lower accuracy but higher speed. A <i>larger</i> model, indicated by
        both
        <i>architecture</i> and <i>multiplier</i> dropdown, results in higher accuracy but lower speed.
      </p>
    </div>

    <div class="footer-menu">
      <i class="material-icons switch-camera">switch_camera</i>
      <i class="material-icons mask mode active">portrait</i>
      <i class="material-icons mode bokeh">blur_on</i>
      <i class="material-icons mode part-map">format_color_fill</i>
      <i class="material-icons high-accuracy">high_quality</i>
    </div>
  </div>
  <script src="bundle.js"></script>
</body>

</html>
