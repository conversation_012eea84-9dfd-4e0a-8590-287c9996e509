{"name": "tfjs-models", "version": "0.0.1", "description": "", "main": "index.js", "license": "Apache-2.0", "private": true, "engines": {"node": ">=8.9.0"}, "dependencies": {"@tensorflow-models/body-pix": "link:../", "@tensorflow-models/posenet": "^2.2.2", "@tensorflow/tfjs-backend-webgl": "^4.22.0", "@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0", "stats.js": "0.17.0"}, "scripts": {"watch": "esbuild index.js --bundle --outfile=bundle.js --target=es6 --servedir=. --serve --sourcemap --sources-content=true --preserve-symlinks", "build": "mkdirp dist && cp index.html dist && esbuild index.js --bundle --target=es6 --outfile=dist/bundle.js --sourcemap --sources-content=true --preserve-symlinks --minify", "lint": "eslint .", "build-model": "cd .. && yarn && yarn build-npm", "build-deps": "yarn build-model"}, "browser": {"crypto": false}, "devDependencies": {"clang-format": "~1.8.0", "dat.gui": "~0.7.9", "esbuild": "^0.17.10", "eslint": "~8.35.0", "eslint-config-google": "~0.14.0", "mkdirp": "^2.1.3"}, "resolutions": {"is-svg": "4.3.1"}, "eslintConfig": {"extends": "google", "rules": {"require-jsdoc": 0, "valid-jsdoc": 0}, "env": {"es6": true}, "parserOptions": {"ecmaVersion": 8, "sourceType": "module"}}, "eslintIgnore": ["dist/"]}