{"name": "@tensorflow-models/body-pix", "version": "2.2.0", "description": "Pretrained BodyPix model in TensorFlow.js", "main": "dist/index.js", "jsnext:main": "dist/body-pix.esm.js", "module": "dist/body-pix.esm.js", "unpkg": "dist/body-pix.min.js", "jsdelivr": "dist/body-pix.min.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/tensorflow/tfjs-models.git"}, "peerDependencies": {"@tensorflow/tfjs-backend-webgl": "^4.22.0", "@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0"}, "devDependencies": {"@babel/polyfill": "^7.10.4", "@tensorflow/tfjs-backend-webgl": "^4.22.0", "@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0", "@types/jasmine": "^4.3.1", "jasmine": "~4.5.0", "jasmine-core": "~4.5.0", "karma": "~6.4.1", "karma-browserstack-launcher": "~1.6.0", "karma-chrome-launcher": "~3.1.1", "karma-firefox-launcher": "~2.1.2", "karma-jasmine": "~5.1.0", "karma-typescript": "~5.5.3", "karma-typescript-es6-transform": "^5.0.2", "rimraf": "~4.1.2", "rollup": "~3.17.3", "ts-node": "~10.9.1", "tslint": "~6.1.3", "typescript": "~5.1.6", "yalc": "^1.0.0-pre.27"}, "scripts": {"build": "rimraf dist && tsc", "publish-local": "yarn build && rollup -c && yalc publish", "test": "karma start", "test-ci": "karma start --singleRun --browsers=bs_chrome_mac", "build-npm": "yarn build && rollup -c", "lint": "tslint -p . -t verbose"}, "license": "Apache-2.0", "dependencies": {}}