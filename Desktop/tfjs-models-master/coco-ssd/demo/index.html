<!-- Copyright 2019 Google LLC. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================-->
<!doctype html>
<html>

<body>
  <h1>TensorFlow.js Object Detection</h1>
  <select id='base_model'>
    <option value="lite_mobilenet_v2">SSD Lite Mobilenet V2</option>
    <option value="mobilenet_v1">SSD Mobilenet v1</option>
    <option value="mobilenet_v2">SSD Mobilenet v2</option>
  </select>
  <button type="button" id="run">Run</button>
  <button type="button" id="toggle">Toggle Image</button>
  <div>
    <img id="image" />
    <canvas id="canvas" width="600" height="399"></canvas>
  </div>
</body>
<script src="index.js"></script>


</html>