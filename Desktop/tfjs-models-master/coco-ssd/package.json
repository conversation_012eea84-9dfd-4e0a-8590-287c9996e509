{"name": "@tensorflow-models/coco-ssd", "version": "2.2.2", "description": "Object detection model (coco-ssd) in TensorFlow.js", "main": "dist/coco-ssd.node.js", "unpkg": "dist/coco-ssd.min.js", "jsdelivr": "dist/coco-ssd.min.js", "jsnext:main": "dist/coco-ssd.es2017.esm.min.js", "module": "dist/coco-ssd.es2017.esm.min.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/tensorflow/tfjs-models.git"}, "peerDependencies": {"@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^11.0.2", "@rollup/plugin-node-resolve": "^7.1.1", "@rollup/plugin-typescript": "^3.0.0", "@tensorflow/tfjs-backend-cpu": "^4.22.0", "@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0", "@types/jasmine": "~2.8.8", "babel-core": "~6.26.0", "babel-plugin-transform-runtime": "~6.23.0", "jasmine-core": "~3.5.0", "rimraf": "~2.6.2", "rollup": "~2.3.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-visualizer": "~5.9.2", "ts-node": "~8.8.2", "tslint": "~5.18.0", "typescript": "~5.1.6", "yalc": "~1.0.0-pre.21"}, "scripts": {"build": "rimraf dist && tsc", "lint": "tslint -p . -t verbose", "publish-local": "yarn build && rollup -c && yalc push", "build-npm": "yarn build && rollup -c --visualize --npm", "test": "ts-node --skip-ignore --project tsconfig.test.json run_tests.ts"}, "license": "Apache-2.0"}