{"name": "@tensorflow-models/universal-sentence-encoder", "version": "1.3.3", "description": "Universal Sentence Encoder lite in TensorFlow.js", "main": "dist/index.js", "jsnext:main": "dist/universal-sentence-encoder.esm.js", "module": "dist/universal-sentence-encoder.esm.js", "unpkg": "dist/universal-sentence-encoder.min.js", "jsdelivr": "dist/universal-sentence-encoder.min.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/tensorflow/tfjs-models.git"}, "peerDependencies": {"@tensorflow/tfjs-converter": "^3.6.0", "@tensorflow/tfjs-core": "^3.6.0"}, "devDependencies": {"@tensorflow/tfjs-backend-cpu": "^3.6.0", "@tensorflow/tfjs-converter": "^3.6.0", "@tensorflow/tfjs-core": "^3.6.0", "@types/jasmine": "~2.5.53", "jasmine": "^3.3.1", "jasmine-core": "^3.3.0", "rimraf": "~2.6.2", "rollup": "~0.58.2", "rollup-plugin-node-resolve": "~3.3.0", "rollup-plugin-typescript2": "~0.13.0", "rollup-plugin-uglify": "~3.0.0", "ts-node": "~5.0.0", "tslint": "~5.18.0", "typescript": "3.5.3", "yalc": "^1.0.0-pre.50"}, "scripts": {"build": "rimraf dist && tsc", "publish-local": "yarn build && rollup -c && yalc push", "test": "ts-node --skip-ignore --project tsconfig.test.json run_tests.ts", "build-npm": "yarn build && rollup -c", "lint": "tslint -p . -t verbose"}, "license": "Apache-2.0"}