{"name": "tfjs-mobilenet-demo", "version": "0.0.1", "description": "", "main": "index.js", "license": "Apache-2.0", "private": true, "engines": {"node": ">=8.9.0"}, "dependencies": {"@tensorflow-models/mobilenet": "file:../dist", "@tensorflow/tfjs-backend-cpu": "^4.22.0", "@tensorflow/tfjs-backend-webgl": "^4.22.0", "@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0"}, "scripts": {"watch": "cross-env NODE_ENV=development parcel index.html --no-hmr --open", "build": "cross-env NODE_ENV=production parcel build index.html --public-url ./", "build-deps": "yarn build-model && yarn build", "build-model": "cd .. && yarn && yarn build-npm"}, "browser": {"crypto": false}, "devDependencies": {"@babel/core": "7.7.5", "@babel/plugin-transform-runtime": "^7.7.6", "@babel/preset-env": "^7.7.6", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-env": "^1.7.0", "clang-format": "~1.2.2", "cross-env": "^5.2.0", "parcel-bundler": "~1.12.5"}, "resolutions": {"is-svg": "4.3.1"}}