{"name": "@tensorflow-models/mobilenet", "version": "2.1.0", "description": "Pretrained MobileNet in TensorFlow.js", "main": "dist/index.js", "unpkg": "dist/mobilenet.min.js", "jsdelivr": "dist/mobilenet.min.js", "jsnext:main": "dist/mobilenet.esm.js", "module": "dist/mobilenet.esm.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/tensorflow/tfjs-models.git"}, "peerDependencies": {"@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0"}, "devDependencies": {"@tensorflow/tfjs-backend-cpu": "^4.22.0", "@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0", "@types/jasmine": "~2.5.53", "@rollup/plugin-typescript": "^3.0.0", "babel-core": "^6.26.0", "babel-plugin-transform-runtime": "~6.23.0", "jasmine": "~3.1.0", "jasmine-core": "~3.1.0", "rimraf": "~2.6.2", "rollup": "~0.58.2", "rollup-plugin-node-resolve": "~3.3.0", "rollup-plugin-typescript2": "~0.13.0", "rollup-plugin-uglify": "~3.0.0", "ts-node": "~5.0.0", "tslint": "~5.18.0", "typescript": "4.4.4"}, "scripts": {"build": "rimraf dist && tsc", "build-npm": "yarn build && rollup -c", "publish-local": "yarn build && rollup -c && yalc push", "lint": "tslint -p . -t verbose", "test": "ts-node --skip-ignore --project tsconfig.test.json run_tests.ts"}, "license": "Apache-2.0"}