{"name": "@tensorflow-models/face-detection", "version": "1.0.3", "description": "Pretrained face detection model", "main": "dist/index.js", "jsnext:main": "dist/face-detection.esm.js", "module": "dist/face-detection.esm.js", "unpkg": "dist/face-detection.min.js", "jsdelivr": "dist/face-detection.min.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/tensorflow/tfjs-models.git"}, "peerDependencies": {"@mediapipe/face_detection": "~0.4.0", "@tensorflow/tfjs-backend-webgl": "^4.22.0", "@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0"}, "devDependencies": {"@babel/polyfill": "^7.10.4", "@mediapipe/face_detection": "^0.4.1646425229", "@rollup/plugin-commonjs": "^11.0.2", "@rollup/plugin-node-resolve": "^7.1.1", "@rollup/plugin-typescript": "^3.0.0", "@tensorflow/tfjs-backend-cpu": "^4.22.0", "@tensorflow/tfjs-backend-webgl": "^4.22.0", "@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0", "@types/jasmine": "~2.8.8", "@types/offscreencanvas": "2019.7.0", "babel-core": "~6.26.0", "babel-plugin-transform-runtime": "~6.23.0", "jasmine": "~3.1.0", "jasmine-core": "~3.1.0", "karma": "~6.4.0", "karma-browserstack-launcher": "~1.6.0", "karma-chrome-launcher": "~2.2.0", "karma-jasmine": "~1.1.0", "karma-spec-reporter": "~0.0.32", "karma-typescript": "~5.5.3", "karma-typescript-es6-transform": "^5.1.0", "rollup": "~2.3.2", "rollup-plugin-terser": "~7.0.2", "rollup-plugin-visualizer": "~3.3.2", "ts-node": "~8.8.2", "tslint": "^6.1.3", "typescript": "4.8.4", "yalc": "~1.0.0-pre.21"}, "scripts": {"bundle": "rollup -c", "build": "rimraf dist && tsc && yarn bundle", "publish-local": "yarn build && rollup -c && yalc publish", "run-browserstack": "karma start --browserstack", "test-node": "NODE_PRESERVE_SYMLINKS=1 ts-node --skip-ignore --project tsconfig.test.json run_tests.ts", "test": "yarn && karma start", "test-ci": "yarn run-browserstack --browsers=bs_chrome_mac", "build-npm": "yarn build && yarn bundle", "lint": "tslint -p . -t verbose"}, "license": "Apache-2.0", "dependencies": {"rimraf": "^3.0.2", "tslib": "2.4.0"}}