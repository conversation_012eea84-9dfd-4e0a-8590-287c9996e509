<!-- Copyright 2022 Google LLC. All Rights Reserved.
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at
    http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================-->
<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0, user-scalable=no">
  <style>
    body {
      margin: 0;
    }

    #main {
      margin: 0;
      position: relative;
    }

    #canvas-wrapper {
      margin-top: 80px;
      position: relative;
    }

    canvas {
      position: absolute;
      top: 0;
      left: 0;
    }

    #top-bar {
      margin-left: 300px;
      position: relative;
    }
  </style>
</head>

<body>
  <div id="stats"></div>
  <div id="main">
    <div class="container">
      <div id="top-bar">
        <label for="videofile">Upload a video file:</label>
        <input type="file" id="videofile" name="video" accept="video/*">
        <button id="submit">Run</button>
      </div>
    </div>
    <div class="container" id="canvas-wrapper">
      <canvas id="output"></canvas>
      <video id="video">
        <source id="currentVID" src="" type="video/mp4">
      </video>
    </div>
    <div class="container">
      <span id="status"></span>
    </div>
  </div>
</body>
<script src="https://cdnjs.cloudflare.com/ajax/libs/dat-gui/0.7.6/dat.gui.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/stats.js/r16/Stats.min.js"></script>
<script src="src/index.js"></script>

</html>
