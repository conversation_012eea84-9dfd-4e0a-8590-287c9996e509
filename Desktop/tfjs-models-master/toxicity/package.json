{"name": "@tensorflow-models/toxicity", "version": "1.2.2", "description": "Toxicity model in TensorFlow.js", "main": "dist/index.js", "jsnext:main": "dist/toxicity.esm.js", "module": "dist/toxicity.esm.js", "unpkg": "dist/toxicity.min.js", "jsdelivr": "dist/toxicity.min.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/tensorflow/tfjs-models.git"}, "peerDependencies": {"@tensorflow/tfjs-converter": "^4.13.0", "@tensorflow/tfjs-core": "^4.13.0"}, "dependencies": {"@tensorflow-models/universal-sentence-encoder": "~1.2.2"}, "devDependencies": {"@tensorflow/tfjs-converter": "^4.13.0", "@tensorflow/tfjs-core": "^4.13.0", "@types/jasmine": "~3.6.8", "babel-core": "^6.26.3", "jasmine": "^3.3.1", "jasmine-core": "^3.3.0", "rimraf": "~2.6.2", "rollup": "~0.58.2", "rollup-plugin-node-resolve": "~3.3.0", "rollup-plugin-typescript2": "~0.13.0", "rollup-plugin-uglify": "~3.0.0", "ts-node": "~8.8.2", "tslint": "~5.18.0", "typescript": "3.5.3", "yalc": "~1.0.0-pre.50"}, "resolutions": {"is-svg": "4.3.1"}, "scripts": {"build": "rimraf dist && tsc", "publish-local": "yarn build && rollup -c && yalc push", "test": "ts-node --skip-ignore --project tsconfig.test.json run_tests.ts", "build-npm": "yarn build && rollup -c", "lint": "tslint -p . -t verbose"}, "license": "Apache-2.0"}