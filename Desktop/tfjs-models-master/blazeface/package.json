{"name": "@tensorflow-models/blazeface", "version": "0.1.0", "description": "Pretrained face detection model in TensorFlow.js", "main": "dist/index.js", "jsnext:main": "dist/blazeface.esm.js", "module": "dist/blazeface.esm.js", "unpkg": "dist/blazeface.min.js", "jsdelivr": "dist/blazeface.min.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/tensorflow/tfjs-models.git"}, "peerDependencies": {"@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0"}, "devDependencies": {"@tensorflow/tfjs-backend-cpu": "^4.22.0", "@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0", "@types/jasmine": "~2.5.53", "jasmine": "^3.7.0", "jasmine-core": "^3.7.1", "rimraf": "~2.6.2", "rollup": "^3.15.0", "ts-node": "~5.0.0", "tslint": "~6.1.3", "typescript": "^5.1.6", "yalc": "^1.0.0-pre.50"}, "scripts": {"build": "rimraf dist && tsc", "publish-local": "yarn build && rollup -c && yalc publish", "test": "ts-node --skip-ignore --project tsconfig.test.json run_tests.ts", "build-npm": "yarn build && rollup -c", "lint": "tslint -p . -t verbose", "publish-demo": "./scripts/publish-demo.sh"}, "license": "Apache-2.0", "dependencies": {}}