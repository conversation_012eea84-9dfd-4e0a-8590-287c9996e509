/**
 * @license
 * Copyright 2023 Google LLC.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */

import * as tf from '@tensorflow/tfjs-core';
import {GPT2} from './gpt2';
export {GPT2} from './gpt2';

// Note that while `tfjs-core` is available here, we shouldn't import any backends.
// Let the user choose which backends they want in their bundle.
tf; // Prevent it from complaining about unused variables

export async function load(): Promise<GPT2>{
  return new GPT2();
}
