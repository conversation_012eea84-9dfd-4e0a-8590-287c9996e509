# GPT2 demo

## Contents

This demo shows how to use the GPT2 model to generate text.

## Setup

cd into the `gpt2` folder. From the root of the repo, this is located at `gpt2/`. From the demo, it's `../`.

Install dependencies:
```sh
yarn
```

cd into the demo and install dependencies:

```sh
cd demo
yarn
```

build the demo's dependencies. You'll need to re-run this whenever you make changes to the `@tfjs-models/gpt2` package that this demo uses.
```sh
yarn build-deps
```

start the dev demo server:
```sh
yarn watch
```
