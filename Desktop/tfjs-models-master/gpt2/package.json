{"name": "@tfjs-models/gpt2", "version": "0.0.1", "description": "Run GPT2 in the browser with TensorFlow.js", "license": "Apache-2.0", "main": "dist/index.js", "jsnext:main": "dist/gpt2.esm.js", "module": "dist/gpt2.esm.js", "unpkg": "dist/gpt2.min.js", "jsdelivr": "dist/gpt2.min.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/tensorflow/tfjs-models.git"}, "peerDependencies": {"@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0"}, "devDependencies": {"@tensorflow/tfjs-backend-cpu": "^4.22.0", "@tensorflow/tfjs-backend-webgl": "^4.22.0", "@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0", "@types/jasmine": "^4.3.5", "jasmine": "3.1.x", "karma": "~6.3.1", "karma-browserstack-launcher": "~1.6.0", "karma-chrome-launcher": "~2.2.0", "karma-jasmine": "~1.1.0", "karma-spec-reporter": "~0.0.32", "karma-typescript": "~5.5.1", "karma-typescript-es6-transform": "^5.1.0", "rollup": "^3.27.2", "ts-node": "^10.9.1", "typescript": "^5.1.6"}, "scripts": {"build": "rimraf dist && tsc", "test": "yarn && karma start", "build-npm": "yarn build && rollup -c", "test-node": "NODE_PRESERVE_SYMLINKS=1 ts-node --skip-ignore --project tsconfig.test.json run_tests.ts"}}