{"compilerOptions": {"module": "commonjs", "moduleResolution": "node", "allowJs": false, "noImplicitAny": true, "sourceMap": true, "removeComments": false, "preserveConstEnums": true, "declaration": true, "target": "es5", "lib": ["esnext", "dom"], "outDir": "./dist", "noUnusedLocals": true, "noImplicitReturns": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedParameters": false, "pretty": true, "noFallthroughCasesInSwitch": true, "allowUnreachableCode": false, "skipLibCheck": true}, "include": ["src/"], "exclude": ["node_modules/"]}