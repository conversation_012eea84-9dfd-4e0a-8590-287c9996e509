{"name": "audio-command-model-node", "version": "0.0.1", "description": "tfjs audio command model training in node.js", "main": "./cli", "license": "Apache-2.0", "private": true, "bin": "./cli", "dependencies": {"@tensorflow/tfjs": "^3.3.0", "@tensorflow/tfjs-node": "^3.3.0", "chalk": "^2.4.1", "vorpal": "1.12.0", "node-wav": "^0.0.2", "ora": "^2.1.0", "ts-node": "7.0.0", "dct": "^0.0.3", "kissfft-js": "0.1.8"}, "scripts": {"build": "tsc", "lint": "tslint -p . -t verbose", "start": "nodemon --exec ts-node -- cli.ts", "ts-node": "ts-node cli.ts"}, "devDependencies": {"@types/chalk": "^2.2.0", "@types/node": "^10.5.2", "@types/ora": "^1.3.4", "@types/inquirer": "^0.0.42", "@types/minimist": "^1.2.0", "clang-format": "~1.2.2", "typescript": "3.5.3", "nodemon": "1.18.2", "tslint": "~6.1.3", "tslint-no-circular-imports": "~0.7.0"}}