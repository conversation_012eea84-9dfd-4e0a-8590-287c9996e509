{"search.exclude": {"**/node_modules": true, "coverage/": true, "**/dist/": true, "**/yarn.lock": true, "**/.rpt2_cache/": true, "**/.yalc/": true}, "tslint.enable": true, "tslint.run": "onType", "tslint.configFile": "tslint.json", "files.trimTrailingWhitespace": true, "editor.tabSize": 2, "editor.insertSpaces": true, "[typescript]": {"editor.formatOnSave": true}, "editor.rulers": [80], "clang-format.style": "Google", "files.insertFinalNewline": true, "editor.detectIndentation": false, "editor.wrappingIndent": "none", "typescript.tsdk": "./node_modules/typescript/lib", "clang-format.executable": "${workspaceRoot}/node_modules/.bin/clang-format"}