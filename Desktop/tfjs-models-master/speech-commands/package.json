{"name": "@tensorflow-models/speech-commands", "version": "0.5.4", "description": "Speech-command recognizer in TensorFlow.js", "main": "dist/index.js", "unpkg": "dist/speech-commands.min.js", "jsdelivr": "dist/speech-commands.min.js", "jsnext:main": "dist/speech-commands.esm.js", "module": "dist/speech-commands.esm.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/tensorflow/tfjs-models.git"}, "peerDependencies": {"@tensorflow/tfjs-core": "^4.13.0", "@tensorflow/tfjs-data": "^4.13.0", "@tensorflow/tfjs-layers": "^4.13.0"}, "devDependencies": {"@tensorflow/tfjs-core": "^4.13.0", "@tensorflow/tfjs-data": "^4.13.0", "@tensorflow/tfjs-layers": "^4.13.0", "@tensorflow/tfjs-node": "^4.13.0", "@types/jasmine": "~2.8.8", "@types/rimraf": "^2.0.2", "@types/tempfile": "^2.0.0", "babel-core": "~6.26.0", "babel-plugin-transform-runtime": "~6.23.0", "clang-format": "^1.2.4", "dct": "^0.0.3", "jasmine": "^3.2.0", "jasmine-core": "^3.2.1", "kissfft-js": "^0.1.8", "rimraf": "2.6.2", "rollup": "~0.58.2", "rollup-plugin-node-resolve": "~3.3.0", "rollup-plugin-typescript2": "~0.13.0", "rollup-plugin-uglify": "~3.0.0", "tempfile": "2.0.0", "ts-node": "~5.0.0", "tslib": "1.8.0", "tslint": "~5.18.0", "tslint-no-circular-imports": "^0.6.1", "typescript": "~3.5.3", "yalc": "~1.0.0-pre.21"}, "scripts": {"build": "tsc", "lint": "tslint -p . -t verbose", "publish-local": "yarn build && rollup -c && yalc push", "build-npm": "yarn build && rollup -c", "test": "ts-node --skip-ignore --project tsconfig.test.json run_tests.ts"}, "license": "Apache-2.0"}