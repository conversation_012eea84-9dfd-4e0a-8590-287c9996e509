{"name": "tfjs-models-speech-commands-demo", "version": "0.0.1", "description": "", "main": "index.js", "license": "Apache-2.0", "private": true, "engines": {"node": ">=8.9.0"}, "dependencies": {"@tensorflow-models/speech-commands": "file:../dist", "stats.js": "^0.17.0"}, "scripts": {"build-model": "cd .. && yarn && yarn build", "watch": "yarn build-model && cross-env NODE_OPTIONS=--max_old_space_size=4096 NODE_ENV=development parcel index.html --no-hmr --open", "build": "yarn build-model && cross-env NODE_OPTIONS=--max_old_space_size=4096 NODE_ENV=production parcel build index.html --public-url ./", "lint": "eslint .", "link-local": "yalc link @tensorflow-models/speech-commands"}, "browser": {"crypto": false}, "devDependencies": {"@babel/core": "^7.0.0-0", "@babel/plugin-transform-runtime": "^7.1.0", "babel-polyfill": "~6.26.0", "babel-preset-env": "~1.6.1", "babel-preset-es2017": "^6.24.1", "clang-format": "~1.2.2", "cross-env": "^5.2.0", "dat.gui": "^0.7.1", "eslint": "^4.19.1", "eslint-config-google": "^0.9.1", "parcel-bundler": "~1.12.5", "plotly.js-dist": "^1.39.4", "yalc": "~1.0.0-pre.50"}, "resolutions": {"is-svg": "4.3.1"}, "eslintConfig": {"extends": "google", "rules": {"require-jsdoc": 0, "valid-jsdoc": 0}, "env": {"es6": true}, "parserOptions": {"ecmaVersion": 8, "sourceType": "module"}}, "eslintIgnore": ["dist/"]}