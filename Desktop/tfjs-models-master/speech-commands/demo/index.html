<!DOCTYPE html>

<html>

<head>
  <meta charset="UTF-8">
  <title>TensorFlow.js Speech Commands Model Demo</title>
  <link href="style.css" rel="stylesheet" type="text/css">
  <meta name="viewport" content="width=device-width, initial-scale=1">
</head>

<body>
  <div class="start-stop">
    <button id="start" disabled="true">Start</button>
    <button id="stop" disabled="true">Stop</button>
  </div>
  <div class='main-model'>
    <div class="settings">
      <span style="display:none">Prob. threshold:</span>
      <input style="display:none" class="settings" size="5" id="proba-threshold" value="0.75">
    </div>
    <div id="candidate-words" class="candidate-words-hidden"></div>
  </div>
  <div class="footer" style="display: none;">
    <textarea id="status-display" style="display: none" cols="80" readonly="true"></textarea>
  </div>
  <div class="transfer-learn-section">
    <input id="transfer-model-name" size="20" placeholder="model name">
    <input id="learn-words" size="36" value="_background_noise_,red,green">
    <select id="duration-multiplier">
      <option value="1">Duration x1</option>
      <option value="2" selected="true">Duration x2</option>
    </select>

    <input type="checkbox" id="include-audio-waveform">
    <span id="include-audio-waveform-label">Include audio waveform</span>

    <button id="enter-learn-words" disabled="true">Enter transfer words</button>

    <div id="transfer-learn-history"></div>
    <div id="collect-words"></div>

    <div class="collapsible-region">
      <button id="dataset-io">Dataset IO >></button>
      <div class="collapsible-region-inner" id="dataset-io-inner">
        <div>
          <button id="download-dataset" disabled="true">↓ Download dataset as file</button>
          <div>
            <input type="file" id="dataset-file-input">
            <button id="upload-dataset">↑ Upload dataset</button>
            <button id="eval-model-on-dataset">Evaluate model on dataset</button>
          </div>
        </div>
      </div>
    </div>

    <div class="settings">
      <span>Epochs:</span>
      <input class="settings" size="5" id="epochs" value="100">
      <span>Fine-tuning (FT) epochs:</span>
      <input class="settings" size="5" id="fine-tuning-epochs" value="0">
      <span>Augment by mixing noise:</span>
      <input type="checkbox" id="augment-by-mixing-noise">
      <button id="start-transfer-learn" disabled="true">Start transfer learning</button>
    </div>

    <div id="plots">
      <div id="loss-plot" class="plots"></div>
      <div id="accuracy-plot" class="plots"></div>
      <div>
        <div>
          <span id="eval-results" class="eval-results"></span>
        </div>
        <div id="roc-plot" class="plots"></div>
      </div>
    </div>

    <div class="collapsible-region">
      <button id="model-io">Model IO >></button>
      <div class="collapsible-region-inner" id="transfer-model-save-load-inner">
        <div>
          <button id="load-transfer-model" disabled="true">Load:</button>
          <select id="saved-transfer-models">
            <option value="1"></option>
          </select>
          <button id="delete-transfer-model" disabled="true">Delete</button>
        </div>
        <div>
          <button id="save-transfer-model" disabled="true">Save model</button>
        </div>
      </div>
    </div>
  </div>

  <script src="index.js"></script>
</body>

</html>
