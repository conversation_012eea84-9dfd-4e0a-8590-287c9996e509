/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */

import {Action, createReducer, on} from '@ngrx/store';

import {ModelItem} from '../models/model_item';

import * as Actions from './actions';
import {initialState} from './state';

/** Reducer for `allModelItems`. */
export function allModelItemsReducer(
    state: ModelItem[]|undefined = initialState.allModelItems, action: Action) {
  return createReducer(
      initialState.allModelItems,

      on(Actions.addModelItemsFromInit,
         (state, {items}) => [...state, ...items]),

      )(state, action);
}
