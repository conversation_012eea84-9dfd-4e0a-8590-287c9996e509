/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */

import {ChangeDetectionStrategy, Component} from '@angular/core';
import {select, Store} from '@ngrx/store';
import {NgrxMediaQueriesFacade} from '@yoozly/ngrx-mediaqueries';

import {ModelItemService} from '../services/model_item_service';
import {selectSelectedModelItemId} from '../store/selectors';
import {AppState} from '../store/state';

/** The main app component. */
@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppComponent {
  isSmallScreen$ = this.mqServide.getMediaQueryState('small-screen');
  selectedItemId$ = this.store.pipe(select(selectSelectedModelItemId));

  constructor(
      private readonly store: Store<AppState>,
      private readonly modelItemService: ModelItemService,
      private readonly mqServide: NgrxMediaQueriesFacade,
  ) {
    this.modelItemService.registerAllModelItems();
  }
}
