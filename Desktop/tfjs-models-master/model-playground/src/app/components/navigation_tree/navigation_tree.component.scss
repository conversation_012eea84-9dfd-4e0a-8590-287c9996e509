/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */

.container {
  background-color: #eee;
  border-right: 1px solid #ccc;
  box-sizing: border-box;
  height: 100%;
  width: 200px;
}

.title {
  font-size: 24px;
  font-weight: 500;
  padding: 20px;
}

.item {
  cursor: pointer;
  padding: 20px;

  &:hover {
    background-color: yellow;
  }

  &.selected {
    color: red;
    font-weight: 500;
  }
}
