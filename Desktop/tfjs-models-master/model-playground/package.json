{"name": "model-playground", "version": "0.0.1", "license": "Apache-2.0", "private": true, "dependencies": {"@angular/animations": "~12.0.4", "@angular/cdk": "~12.0.4", "@angular/common": "~12.0.4", "@angular/compiler": "~12.0.4", "@angular/core": "~12.0.4", "@angular/forms": "~12.0.4", "@angular/material": "~12.0.4", "@angular/platform-browser": "~12.0.4", "@angular/platform-browser-dynamic": "~12.0.4", "@angular/router": "~12.0.4", "@ngrx/store": "~12.1.0", "@ngrx/router-store": "~12.1.0", "@ngrx/store-devtools": "~12.1.0", "@yoozly/ngrx-mediaqueries": "^0.0.8", "rxjs": "~6.6.0", "tslib": "^2.1.0", "zone.js": "~0.11.4"}, "peerDependencies": {}, "devDependencies": {"@angular-devkit/build-angular": "~12.0.4", "@angular/cli": "~12.0.4", "@angular/compiler-cli": "~12.0.4", "@types/jasmine": "~3.6.0", "@types/node": "^12.11.1", "jasmine-core": "~3.7.0", "karma": "~6.3.0", "karma-browserstack-launcher": "~1.6.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "tslint": "~6.1.3", "typescript": "~4.2.3"}, "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "lint": "tslint -p . -t verbose", "test": "ng test", "test-ci": "ng test --karma-config karma-ci.conf.js", "deploy-dev": "./scripts/deploy-dev.sh"}}