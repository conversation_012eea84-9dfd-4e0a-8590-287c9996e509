{"name": "@tensorflow-models/qna", "version": "1.0.1", "description": "Question and Answer model (Mobile BERT)", "main": "dist/index.js", "unpkg": "dist/qna.min.js", "jsdelivr": "dist/qna.min.js", "jsnext:main": "dist/qna.esm.js", "module": "dist/qna.esm.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/tensorflow/tfjs-models.git"}, "peerDependencies": {"@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.0", "@rollup/plugin-typescript": "3.1.1", "@tensorflow/tfjs-backend-cpu": "^4.22.0", "@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0", "@types/jasmine": "^3.6.3", "babel-core": "~6.26.3", "babel-plugin-transform-runtime": "~6.23.0", "jasmine": "~3.3.1", "jasmine-core": "~3.3.0", "rimraf": "3.0.2", "rollup": "~2.38.0", "rollup-plugin-uglify": "~3.0.0", "ts-node": "~5.0.0", "tslint": "~6.1.3", "typescript": "5.1.6", "yalc": "~1.0.0-pre.35"}, "scripts": {"build": "rimraf dist && tsc", "lint": "tslint -p . -t verbose", "build-local": "yarn build && rollup -c", "build-npm": "yarn build && rollup -c", "test": "ts-node --skip-ignore --project tsconfig.test.json run_tests.ts"}, "license": "Apache-2.0"}