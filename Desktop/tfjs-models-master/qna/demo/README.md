## QnA Demo

This demo allows you to find answers of a question from the given context.
To use the demo, you can update the text area with your own text, type your
question into the input box, and click the search button. You will see the
answers displayed in the Answers section.

## Setup

`cd` into the demo/ folder:

```sh
cd qna/demo
```

Install dependencies:

```sh
yarn
```

Build linked dependencies:

```sh
yarn build-deps
```

Launch a development server, and watch files for changes. This command will also automatically open
the demo app in your browser.

```sh
yarn watch
```
