{"name": "tfjs-qna-demo", "version": "0.0.1", "description": "", "main": "index.js", "license": "Apache-2.0", "private": true, "engines": {"node": ">=8.9.0"}, "dependencies": {"@tensorflow-models/qna": "file:../dist"}, "scripts": {"watch": "cross-env NODE_ENV=development parcel index.html --no-hmr --open", "build": "cross-env NODE_ENV=production parcel build index.html --public-url ./", "build-deps": "yarn build-qna", "build-qna": "cd .. && yarn && yarn build-npm", "lint": "eslint ."}, "browser": {"crypto": false}, "devDependencies": {"@babel/core": "^7.7.5", "@babel/plugin-transform-runtime": "^7.7.6", "@babel/polyfill": "^7.10.4", "@babel/preset-env": "^7.7.6", "@tensorflow/tfjs-backend-cpu": "^4.22.0", "@tensorflow/tfjs-backend-webgl": "^4.22.0", "@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0", "babel-preset-env": "^1.7.0", "clang-format": "~1.5.0", "cross-env": "^7.0.3", "dat.gui": "^0.7.7", "parcel-bundler": "~1.12.5", "tslint": "~6.1.3", "typescript": "5.1.6"}, "resolutions": {"is-svg": "4.3.1"}, "eslintConfig": {"extends": "google", "rules": {"require-jsdoc": 0, "valid-jsdoc": 0}, "env": {"es6": true}, "parserOptions": {"ecmaVersion": 8, "sourceType": "module"}}, "eslintIgnore": ["dist/"]}