{"name": "@tensorflow-models/tasks", "version": "0.0.1-alpha.8", "description": "Tensorflow.js tasks API", "main": "dist/tfjs-tasks.min.js", "jsnext:main": "dist/tfjs-tasks.esm.js", "jsdelivr": "dist/tfjs-tasks.min.js", "unpkg": "dist/tfjs-tasks.min.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/tensorflow/tfjs-models.git"}, "license": "Apache-2.0", "engines": {"yarn": ">= 1.3.2"}, "dependencies": {}, "peerDependencies": {}, "devDependencies": {"@babel/core": "7.13.14", "@babel/plugin-transform-runtime": "^7.13.10", "@babel/polyfill": "^7.8.7", "@rollup/plugin-commonjs": "^11.0.2", "@rollup/plugin-node-resolve": "^11.2.1", "@rollup/plugin-typescript": "^3.0.0", "@tensorflow/tfjs-converter": "^4.13.0", "@tensorflow/tfjs-core": "^4.13.0", "@tensorflow/tfjs-tflite": "0.0.1-alpha.3", "@tensorflow-models/mobilenet": "link:../mobilenet", "@tensorflow-models/coco-ssd": "link:../coco-ssd", "@tensorflow-models/deeplab": "link:../deeplab", "@tensorflow-models/toxicity": "link:../toxicity", "@tensorflow-models/qna": "link:../qna", "@types/jasmine": "~3.6.9", "clang-format": "~1.5.0", "jasmine": "~3.7.0", "karma": "^6.3.2", "karma-browserstack-launcher": "~1.6.0", "karma-chrome-launcher": "~3.1.0", "karma-firefox-launcher": "~2.1.0", "karma-jasmine": "~4.0.1", "karma-typescript": "~5.5.1", "karma-typescript-es6-transform": "^5.5.1", "postcss": "^8.2.1", "rimraf": "~3.0.2", "rollup": "~2.44.0", "rollup-plugin-terser": "~7.0.2", "rollup-plugin-visualizer": "~5.3.0", "rollup-plugin-uglify": "~6.0.4", "tmp": "^0.2.1", "ts-node": "^5.0.1", "tslib": "^2.1.0", "tslint": "~6.1.3", "tslint-no-circular-imports": "^0.7.0", "typescript": "3.5.3", "yalc": "~1.0.0-pre.50"}, "scripts": {"build": "tsc", "build-npm": "./scripts/build-npm.sh", "build-mobilenet": "cd .. && cd mobilenet && yarn && yarn build-npm", "build-coco-ssd": "cd .. && cd coco-ssd && yarn && yarn build-npm", "build-deeplab": "cd .. && cd deeplab && yarn && yarn build-npm", "build-toxicity": "cd .. && cd toxicity && yarn && yarn build-npm", "build-qna": "cd .. && cd qna && yarn && yarn build-npm", "build-deps": "yarn build-mobilenet && yarn build-coco-ssd && yarn build-deeplab && yarn build-toxicity && yarn build-qna", "lint": "tslint -p . -t verbose", "test": "ts-node --skip-ignore --project tsconfig.test.json run_tests.ts"}}