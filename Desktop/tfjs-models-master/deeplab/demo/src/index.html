<!-- Copyright 2019 Google LLC. All Rights Reserved.
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
============================================================================= -->
<!DOCTYPE html>
<html lang="en">

<head>
  <title>DeepLab Demo</title>
  <link rel="stylesheet" href="index.css" />
  <script src="index.js"></script>
</head>

<body>
  <div class="container">
    <div class="section">
      <nav class="navbar">
        <div class="navbar-brand">
          <h1 class="navbar-item">
            <img alt="TensorFlow.js Logo" src="tfjs-logo.png" />
            <span class="tag is-white is-medium">
              <b>DeepLab Demo</b>
            </span>
          </h1>
        </div>
      </nav>
      <div class="tile is-ancestor flex-should-wrap space-evenly">
        <div class="tile is-parent is-8 min-content">
          <div class="tile is-child">
            <article class="message">
              <div class="message-header">
                <h1>Three types of pre-trained weights are available:</h1>
              </div>
              <div class="message-body">
                <div class="columns is-flex-centered">
                  <div class="column">
                    <div class="card large">
                      <div class="card-content">
                        <div class="content">
                          <b>DeepLab
                            <a href="http://host.robots.ox.ac.uk/pascal/VOC/">
                              Pascal
                            </a>
                          </b>
                        </div>
                      </div>
                      <footer class="card-footer">
                        <a class="button card-footer-item is-unselectable" id="toggle-pascal-image">Load
                          example image</a>
                        <a class="button card-footer-item is-unselectable" id="run-pascal">Run</a>
                      </footer>
                    </div>
                  </div>
                  <div class="column">
                    <div class="card large">
                      <div class="card-content">
                        <div class="content">
                          <b>DeepLab
                            <a href="https://www.cityscapes-dataset.com">
                              Cityscapes
                            </a>
                          </b>
                        </div>
                      </div>
                      <footer class="card-footer">
                        <a class="button card-footer-item is-unselectable" id="toggle-cityscapes-image">Load
                          example image</a>
                        <a class="button card-footer-item is-unselectable" id="run-cityscapes">Run</a>
                      </footer>
                    </div>
                  </div>
                  <div class="column">
                    <div class="card large">
                      <div class="card-content">
                        <div class="content">
                          <b>DeepLab
                            <a href="https://groups.csail.mit.edu/vision/datasets/ADE20K/">
                              ADE20K
                            </a>
                          </b>
                        </div>
                      </div>
                      <footer class="card-footer">
                        <a class="button card-footer-item is-unselectable" id="toggle-ade20k-image">Load
                          example image</a>
                        <a class="button card-footer-item is-unselectable" id="run-ade20k">Run</a>
                      </footer>
                    </div>
                  </div>
                </div>
              </div>
            </article>
            <div class="is-inline-flex is-vcentered flex-should-wrap">
              <div class="is-inline-flex no-breaks m-r-sm">
                <span class="is-vcentered m-r-sm">
                  <b>Quantization:</b>
                </span>
                <div class="control">
                  <div class="select">
                    <select id="quantizationBytes">
                      <option>1</option>
                      <option selected>2</option>
                      <option>4</option>
                    </select>
                  </div>
                </div>
              </div>
              <span class="is-inline-flex no-breaks">
                <b class="m-r-sm">Status:</b>
                <span id="status-message">Loading models...</span>
              </span>
            </div>

          </div>
        </div>
        <div class="tile is-parent is-3">
          <div class="box tile is-child is-on-top is-inline-flex is-flex-column ">
            <div class="notification">
              For best performance, upload images with objects (chairs,
              plants) or animals (humans, cats).
            </div>
            <div class="file is-boxed is-centered">
              <label class="file-label">
                <input class="file-input" type="file" name="image" id="upload-image" />
                <span class="file-cta">
                  <span class="file-label">
                    Upload an image
                  </span>
                </span>
              </label>
            </div>
          </div>
        </div>
      </div>
      <div class="tile is-ancestor">
        <div class="tile is-horizontal">
          <div class="tile is-parent is-5">
            <div class="tile is-child card large is-invisible" id="input-card">
              <header class="card-header">
                <p class="card-header-title">
                  Input image
                </p>
              </header>
              <div class="card-content">
                <figure class="image">
                  <img alt="Input image" id="input-image" src="./placeholder.png" />
                </figure>
              </div>
            </div>
          </div>
          <div class="tile is-parent is-5">
            <div class="tile is-child card large is-invisible" id="output-card">
              <header class="card-header">
                <p class="card-header-title">
                  Segmentation map
                </p>
              </header>
              <div class="card-content">
                <figure class="image">
                  <canvas id="output-image"></canvas>
                </figure>
              </div>
            </div>
          </div>
          <div class="tile is-parent">
            <div class="tile is-child card large is-invisible" id="legend-card">
              <header class="card-header">
                <p class="card-header-title">
                  Legend
                </p>
              </header>
              <div class="card-content">
                <div id="legend" class="columns is-multiline"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>

</html>
