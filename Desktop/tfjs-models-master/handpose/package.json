{"name": "@tensorflow-models/handpose", "version": "0.0.7", "description": "Pretrained hand detection model", "main": "dist/index.js", "jsnext:main": "dist/handpose.esm.js", "module": "dist/handpose.esm.js", "unpkg": "dist/handpose.min.js", "jsdelivr": "dist/handpose.min.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/tensorflow/tfjs-models.git"}, "peerDependencies": {"@tensorflow/tfjs-backend-webgl": "^4.22.0", "@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^11.0.2", "@rollup/plugin-node-resolve": "^7.1.1", "@rollup/plugin-typescript": "^3.0.0", "@tensorflow/tfjs-backend-cpu": "^4.22.0", "@tensorflow/tfjs-backend-webgl": "^4.22.0", "@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0", "@types/jasmine": "~2.8.8", "babel-core": "~6.26.0", "babel-plugin-transform-runtime": "~6.23.0", "jasmine-core": "~3.5.0", "rollup": "~2.3.2", "rollup-plugin-terser": "~7.0.2", "rollup-plugin-visualizer": "~3.3.2", "ts-node": "~8.8.2", "tslint": "~5.18.0", "typescript": "~5.1.6", "yalc": "~1.0.0-pre.21"}, "scripts": {"bundle": "rollup -c", "build": "rimraf dist && tsc && yarn bundle", "publish-local": "yarn build && rollup -c && yalc publish", "test": "ts-node --skip-ignore --project tsconfig.test.json run_tests.ts", "build-npm": "yarn build && yarn bundle", "lint": "tslint -p . -t verbose", "publish-demo": "./scripts/publish-demo.sh"}, "license": "Apache-2.0", "dependencies": {"rimraf": "^3.0.2", "tslib": "^2.6.1"}}