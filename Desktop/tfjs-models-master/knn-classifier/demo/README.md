# KNN Image Classifier Demo

## Contents

### Demo: Camera

The camera demo shows how to create a custom classifier with 3 classes that can be trained in realtime using a webcamera. Hold down the train button to add samples to the classifier, and then let it predict which of the 3 classes that is closest.

## Setup

cd into the demo folder:

```sh
cd knn-classifier/demo
```

Install dependencies and prepare the build directory:

```sh
yarn
```

Build linked dependencies:

```sh
yarn build-deps
```

To watch files for changes, and launch a dev server:

```sh
yarn watch
```
