<!DOCTYPE html>
<html>

<head>
    <title>KNN - Camera Feed Demo</title>
    <style>
    .footer {
        position: fixed;
        left: 0;
        bottom: 0;
        width: 100%;
        color: black;
    }

    .footer-text {
        max-width: 600px;
        text-align: center;
        margin: auto;
    }

    @media only screen and (max-width: 600px) {
      .footer-text, .dg {
        display: none;
      }
    }
    </style>
    <meta name="viewport" content="width=device-width, initial-scale=1">
</head>

<body>
    <div id="info" style='display:none'>
    </div>
    <div id="loading">
        Loading the model...
    </div>

    <div id='main' style='display:none'>
        <video id="video" playsinline style=" -moz-transform: scaleX(-1);
        -o-transform: scaleX(-1);
        -webkit-transform: scaleX(-1);
        transform: scaleX(-1);
        ">
        </video>
    </div>
    <script src="index.js"></script>
</body>

</html>
