{"name": "@tensorflow-models/knn-classifier", "version": "1.2.5", "description": "KNN Classifier for TensorFlow.js", "main": "dist/index.js", "unpkg": "dist/knn-classifier.min.js", "jsdelivr": "dist/knn-classifier.min.js", "jsnext:main": "dist/knn-classifier.esm.js", "module": "dist/knn-classifier.esm.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/tensorflow/tfjs-models.git"}, "peerDependencies": {"@tensorflow/tfjs-backend-cpu": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0"}, "devDependencies": {"@rollup/plugin-node-resolve": "^11.1.0", "@rollup/plugin-typescript": "3.1.1", "@tensorflow/tfjs-backend-cpu": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0", "@types/jasmine": "^3.6.3", "babel-core": "~6.26.0", "babel-plugin-transform-runtime": "~6.23.0", "jasmine": "^3.6.4", "jasmine-core": "~3.1.0", "rimraf": "~2.6.2", "rollup": "~2.38.0", "rollup-plugin-uglify": "~3.0.0", "ts-node": "~5.0.0", "tslint": "~5.18.0", "typescript": "~5.1.6", "yalc": "~1.0.0-pre.21"}, "scripts": {"build": "rimraf dist && tsc", "build-npm": "yarn build && rollup -c", "publish-local": "yarn build && rollup -c && yalc push", "lint": "tslint -p . -t verbose", "test": "ts-node --skip-ignore --project tsconfig.test.json run_tests.ts"}, "license": "Apache-2.0"}