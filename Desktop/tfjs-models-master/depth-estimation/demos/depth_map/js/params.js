/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */

const videoWidth = isMobile() ? 480 : 640;
const videoHeight = isMobile() ? 480 : 480;

const STATE = {
  VisualizeDepth: true,
  MinDepth: 0.3,
  MaxDepth: 0.9,
  DepthCachedFrames: 1,
};

const shaderUniforms = [
  ['VisualizeDepth', 0, 1],
  ['MinDepth', 0, 1],
  ['MaxDepth', 0, 1],
];