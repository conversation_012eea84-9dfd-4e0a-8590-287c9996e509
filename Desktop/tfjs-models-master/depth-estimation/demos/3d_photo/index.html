<!-- Copyright 2022 Google LLC. All Rights Reserved.
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at
    http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================-->
<html>

<head>
  <meta charset="UTF-8">
  <title>Depth TensorFlow.js Demo</title>
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
  <meta http-equiv="Pragma" content="no-cache" />
  <meta http-equiv="Expires" content="0" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans">
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0-beta/css/bootstrap.min.css"
    integrity="sha384-/Y6pD6FV/Vv2HJnA6t+vslU6fwYXjCFtcEpHbNJ0lyAFsXTsjBbfaDjzALeQsN6M" crossorigin="anonymous" />
  <link href="main.css" rel="stylesheet" />

  <script src="https://code.jquery.com/jquery-3.2.1.slim.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.11.0/umd/popper.min.js"></script>
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0-beta/js/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/dat-gui/0.7.6/dat.gui.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs/dist/tf.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/signature_pad@3.0.0-beta.4/dist/signature_pad.umd.min.js"></script>

  <link href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/2.0.0-alpha.2/cropper.min.css" rel="stylesheet">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/2.0.0-alpha.2/cropper.min.js"></script>

  <script src="https://cdn.jsdelivr.net/npm/@tensorflow-models/body-segmentation"></script>
  <script src="https://cdn.jsdelivr.net/npm/@tensorflow-models/depth-estimation"></script>

  <script src="https://unpkg.com/dropzone@5/dist/min/dropzone.min.js"></script>
  <script>
    Dropzone.autoDiscover = false;
  </script>
  <link rel="stylesheet" href="https://unpkg.com/dropzone@5/dist/min/dropzone.min.css" type="text/css" />

  <script src="https://cdn.jsdelivr.net/npm/ccapture.js-npmfixed@1.1.0/build/CCapture.all.min.js"></script>

  <!-- Import maps polyfill -->
  <!-- Remove this when import maps will be widely supported -->
  <script async src="https://unpkg.com/es-module-shims@1.3.6/dist/es-module-shims.js"></script>

  <script type="importmap">
			{
				"imports": {
					"three": "https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.module.min.js"
				}
			}
		</script>

  <!-- Global site tag (gtag.js) - Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-N15RXSM4WY"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());

    gtag('config', 'G-N15RXSM4WY');
  </script>

</head>

<body>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>

  <script src="./shaders/vertex_shader.js"></script>
  <script src="./shaders/fragment_shader.js"></script>
  <script src="./shaders/vertex_shader_3d_photo.js"></script>
  <script src="./shaders/fragment_shader_3d_photo.js"></script>

  <div class="container">
    <div class="row">
      <div class="col-md-4" style="width: 30%; display: block; float:left;">
        <div id="title">3D Portrait</div>
        <br />
        <div id="description">Create a 3D Portrait with your own photo!
        </div>
        <br />
        <br />
        <br />
        <div id="example">Try with example images!</div>
        <button type="button" class="btn btn-warning" onclick="loadPreset(1);" autofocus>1</button>
        <button type="button" class="btn btn-warning" onclick="loadPreset(2);">2</button>
        <button type="button" class="btn btn-warning" onclick="loadPreset(3);">3</button>
        <button type="button" class="btn btn-warning" onclick="loadPreset(4);">4</button>
        <button type="button" class="btn btn-warning" onclick="loadPreset(5);">5</button>
        <br>
        <button type="button" class="btn btn-warning" onclick="loadPreset(6);">6</button>
        <button type="button" class="btn btn-warning" onclick="loadPreset(7);">7</button>
        <button type="button" class="btn btn-warning" onclick="loadPreset(8);">8</button>
        <button type="button" class="btn btn-warning" onclick="loadPreset(9);">9</button>
        <button type="button" class="btn btn-warning" onclick="loadPreset(10);">10</button>
        <br />
        <br />
        <br />
        <div id="example">Now with your images!</div>

        <div id="instruction">
          <br />
          1. Drag and drop or click to upload a portrait image.
          <br />
          2. You can crop the image after uploading.
          <br />
          3. Processing happens automatically once the image is uploaded.
          <br />
          4. Your 3D photo is ready to be downloaded (in GIF or WebM format) and used in various other applications.
          <br />
          <br />
          Note: The first processing will take longer for initialization (>3s), while subsequent predictions are faster
          (&lt;0.2s).
          <br />
          <br />
          Please refer to our <a
            href="https://github.com/tensorflow/tfjs-models/tree/master/depth-estimation">GitHub</a> Portrait
          Depth API repo for more details.
          <br />
          <br />
          For 3D portraits effects and more creative usage (e.g., depth-of-field, depth-scanning effects) of depth maps,
          you can checkout <a href="https://augmentedperception.github.io/depthlab/">DepthLab</a>.
        </div>
        <br />
      </div>
      <div class="col-md-8" style="width: 70%; display: block; float: right;" id="right-side">
        <br /><br /><br />
        <div class="row">
          <div class="col-6">
            <span id="perf"> </span>
          </div>
        </div>
        <br />
        <div class="row">
          <div class="col-3">
            <img id="im1" class="input-image" alt="" src="images/im0.jpg" />
          </div>
          <div class="col-3 desktop">
            <canvas id="masked" height="256" width="192"></canvas>
          </div>

          <div class="col-3 desktop">
            <div id="GL" style="width:calc(192px); height: 256px; background-color: #f00;"></div>

          </div>
          <div class="col-3">
            <div id="GL2" style="width:calc(192px); height: 256px; background-color: #f00;"></div>
          </div>
        </div>
        <div class="row">
          <div class="col-3 caption">
            ORIGINAL
          </div>
          <div class="col-3 caption desktop">
            SEGMENTED
          </div>
          <div class="col-3 caption desktop">
            DEPTH
          </div>
          <div class="col-3 caption">
            3D IMAGE!
          </div>
        </div>
        <br />
        <div class="row">
          <div class="col-3">
            <button type="button" class="btn btn-secondary" id="predict" onclick="predict();" disabled>Measure
              Latency</button>
          </div>
          <div class="col-6 text-center">
            <button type="button" class="btn btn-secondary" id="delete-upload" style="visibility:hidden;">Delete
              Upload</button>
          </div>
          <div class="dropdown col-3">
            <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton"
              data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
              Download Animation
            </button>
            <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
              <a class="dropdown-item" id="download-anim-gif">GIF</a>
              <a class="dropdown-item" id="download-anim-webm">WebM</a>
            </div>
          </div>
        </div>
        <br />
        <form action="/url" class="dropzone" id="dropzone" height="600">
          <div class="dz-message" data-dz-message><span>Drag & drop or click to upload images</span></div>
        </form>
        <div class="result" id="uploaded-img"></div>
        <br />
      </div>
    </div>
    <canvas id="result" height="256" width="192" style="visibility: hidden;"></canvas>

    <canvas id="resize" height="256" width="192" style="visibility: hidden;"></canvas>

    <script src="./js/depth_scene.js"></script>
    <script src="./js/depth.js"></script>
    <script src="./js/globals.js"></script>
    <script type="module" src="./js/index.js"></script>
</body>

</html>
