# Demos

Try our demos and get inspired with what you can do with depth-estimation models!

## Table of Contents
1. [3D Photo Demo](#3d-photo-demo)

2. [Relighting Demo](#relighting-demo)

3. [Depth Map Demo](#depth-map-demo)

4. [How to Run a Demo](#how-to-run-a-demo)

-------------------------------------------------------------------------------

## 3D Photo Demo
This demo allows to upload a portrait image, which is turned into a 3D photo
animation. It works on laptops, iPhones and android phones.

[Demo](https://storage.googleapis.com/tfjs-models/demos/3dphoto/index.html)

## Relighting Demo
This demo uses your camera to get live stream and allows you to place
a light source or point lights to change the lighting of the scene.
It works on laptops, iPhones and android phones.

[Demo](https://storage.googleapis.com/tfjs-models/demos/relighting/index.html)

## Depth Map Demo
This demo uses your camera to get live stream and visualizes the depth in real-time.
It works on laptops, iPhones and android phones.

[Demo](https://storage.googleapis.com/tfjs-models/demos/depthmap/index.html)

## How to Run a Demo
If you want to try any of the demos, follow these steps:

1. Go to the demo folder, e.g. `cd live_video`

2. Host the folder contents online

3. Access the `index.html` page
