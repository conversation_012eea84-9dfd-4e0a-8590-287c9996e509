/**
 * @license
 * Copyright 2022 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */

import {load as loadARPortraitDepth} from './ar_portrait_depth/estimator';
import {ARPortraitDepthModelConfig} from './ar_portrait_depth/types';
import {DepthEstimator} from './depth_estimator';
import {SupportedModels} from './types';

/**
 * Create a depth estimator instance.
 *
 * @param model The name of the pipeline to load.
 * @param modelConfig The configuration for the pipeline to load.
 */
export async function createEstimator(
    model: SupportedModels,
    modelConfig?: ARPortraitDepthModelConfig): Promise<DepthEstimator> {
  switch (model) {
    case SupportedModels.ARPortraitDepth:
      return loadARPortraitDepth(modelConfig);
    default:
      throw new Error(`${model} is not a supported model name.`);
  }
}
