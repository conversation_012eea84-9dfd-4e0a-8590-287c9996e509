{"name": "@tensorflow-models/posenet", "version": "2.2.2", "description": "Pretrained PoseNet model in TensorFlow.js", "main": "dist/index.js", "jsnext:main": "dist/posenet.esm.js", "module": "dist/posenet.esm.js", "unpkg": "dist/posenet.min.js", "jsdelivr": "dist/posenet.min.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/tensorflow/tfjs-models.git"}, "peerDependencies": {"@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0"}, "devDependencies": {"@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0", "@tensorflow/tfjs-backend-cpu": "^4.22.0", "@types/jasmine": "~3.3.15", "babel-core": "^6.26.0", "babel-plugin-transform-runtime": "~6.23.0", "jasmine": "^3.2.0", "jasmine-core": "~3.5.0", "rimraf": "~2.6.2", "rollup": "~0.58.2", "rollup-plugin-node-resolve": "~3.3.0", "rollup-plugin-typescript2": "~0.13.0", "rollup-plugin-uglify": "~3.0.0", "ts-node": "~8.8.2", "tslint": "~5.18.0", "typescript": "5.1.6", "yalc": "~1.0.0-pre.27"}, "scripts": {"build": "rimraf dist && tsc", "test": "ts-node --skip-ignore --project tsconfig.test.json run_tests.ts", "publish-local": "yarn build && rollup -c && yalc push", "build-npm": "yarn build && rollup -c", "dev": "cd demo && yarn watch", "lint": "tslint -p . -t verbose"}, "license": "Apache-2.0"}