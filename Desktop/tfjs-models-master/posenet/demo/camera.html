<!DOCTYPE html>
<html>

<head>
    <title>PoseNet - Camera Feed Demo</title>
    <style>
        .footer {
            position: fixed;
            left: 0;
            bottom: 0;
            width: 100%;
            color: black;
        }

        .footer-text {
            max-width: 600px;
            text-align: center;
            margin: auto;
        }

        @media only screen and (max-width: 600px) {

            .footer-text,
            .dg {
                display: none;
            }
        }

        /*
         *  The following loading spinner CSS is from SpinKit project
         *  https://github.com/tobiasahlin/SpinKit
         */
        .sk-spinner-pulse {
            width: 20px;
            height: 20px;
            margin: auto 10px;
            float: left;
            background-color: #333;
            border-radius: 100%;
            -webkit-animation: sk-pulseScaleOut 1s infinite ease-in-out;
            animation: sk-pulseScaleOut 1s infinite ease-in-out;
        }

        @-webkit-keyframes sk-pulseScaleOut {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
            }

            100% {
                -webkit-transform: scale(1.0);
                transform: scale(1.0);
                opacity: 0;
            }
        }

        @keyframes sk-pulseScaleOut {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
            }

            100% {
                -webkit-transform: scale(1.0);
                transform: scale(1.0);
                opacity: 0;
            }
        }

        .spinner-text {
            float: left;
        }
    </style>
    <meta name="viewport" content="width=device-width, initial-scale=1">
</head>

<body>
    <div id="info" style='display:none'>
    </div>
    <div id="loading" style='display:flex'>
        <div class="spinner-text">
            Loading PoseNet model...
        </div>
        <div class="sk-spinner sk-spinner-pulse"></div>
    </div>
    <div id='main' style='display:none'>
        <video id="video" playsinline style="display: none;">
        </video>
        <canvas id="output" />
    </div>
    <div class="footer">
        <div class="footer-text">
            <p>
                PoseNet runs with either a <strong>single-pose</strong> or <strong>multi-pose</strong> detection
                algorithm. The single person pose detector is faster and more accurate but requires only one subject
                present in the image.
                <br>
                <br> The <strong>output stride</strong> and <strong>input resolution</strong> have the largest effects
                on accuracy/speed. A <i>higher</i> output stride results in lower accuracy but higher speed. A
                <i>higher</i> image scale factor results in higher accuracy but lower speed. </p>
        </div>
    </div>
    <script src="camera.js"></script>
</body>

</html>
