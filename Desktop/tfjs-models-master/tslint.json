{"extends": ["tslint-no-circular-imports"], "rulesDirectory": ".tslint", "rules": {"array-type": [true, "array-simple"], "arrow-return-shorthand": true, "ban": [true, ["fit"], ["fdescribe"], ["xit"], ["xdescribe"], ["fitAsync"], ["xitAsync"], ["fitFakeAsync"], ["xitFakeAsync"]], "ban-types": [true, ["Object", "Use {} instead."], ["String", "Use 'string' instead."], ["Number", "Use 'number' instead."], ["Boolean", "Use 'boolean' instead."]], "no-return-await": true, "class-name": true, "curly": true, "interface-name": [true, "never-prefix"], "jsdoc-format": true, "forin": false, "label-position": true, "max-line-length": {"options": {"limit": 80, "ignore-pattern": "^import |^export |https?://"}}, "new-parens": true, "no-angle-bracket-type-assertion": true, "no-any": true, "no-construct": true, "no-consecutive-blank-lines": true, "no-debugger": true, "no-default-export": true, "no-imports-from-dist": true, "no-inferrable-types": true, "no-namespace": [true, "allow-declarations"], "no-reference": true, "no-require-imports": true, "no-string-throw": true, "no-unused-expression": true, "no-var-keyword": true, "no-unnecessary-type-assertion": true, "object-literal-shorthand": true, "only-arrow-functions": [true, "allow-declarations", "allow-named-functions"], "prefer-const": true, "quotemark": [true, "single"], "radix": true, "restrict-plus-operands": true, "semicolon": [true, "always", "ignore-bound-class-methods"], "switch-default": true, "triple-equals": [true, "allow-null-check"], "use-isnan": true, "use-default-type-parameter": true, "variable-name": [true, "check-format", "ban-keywords", "allow-leading-underscore", "allow-trailing-underscore"]}}